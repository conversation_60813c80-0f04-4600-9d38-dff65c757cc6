"""
Truthfulness Steering Pipeline

This module provides classes for creating and managing functional activations
from benchmark datasets for truthfulness steering applications.
"""

import os
import json
import re
import logging
import pandas as pd
import torch
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from dataclasses import dataclass

from .model_loader import UniversalModelLoader
from .config import ModelConfig
from .sae import SAE
from .steering import (
    KnowledgeSelectionSteering,
    FunctionalActivations,
    MutualInformationResult,
)

logger = logging.getLogger(__name__)


@dataclass
class BenchmarkScenario:
    """A single benchmark scenario for truthfulness steering."""

    task_id: str
    system: str
    user: str
    scheming_response: str
    truthful_response: str
    type: str
    ground_truth: str
    csv_source: str


@dataclass
class SteeringPipelineConfig:
    """Configuration for truthfulness steering pipeline."""

    model_name: str
    sae_path: str
    train_data_dir: str = "./data/mask_benchmark/csv_data/"
    target_layer: int = 16
    steering_alpha: float = 1.5
    top_k_proportion: float = 0.15
    max_train_scenarios: Optional[int] = None
    max_new_tokens: int = 1024
    results_dir: str = "./results/"
    device: str = "auto"
    num_processes: Optional[int] = None
    sae_folder_name: Optional[str] = None


class BenchmarkDataProcessor:
    """Processes benchmark CSV data into scenarios for truthfulness steering."""

    @staticmethod
    def load_and_process_data(
        data_dir: str, max_scenarios: Optional[int] = None
    ) -> List[BenchmarkScenario]:
        """Load and process all benchmark CSV files from a directory."""
        logger.info(f"Loading benchmark data from {data_dir}")

        if not os.path.exists(data_dir):
            raise ValueError(f"Data directory not found: {data_dir}")

        csv_files = [f for f in os.listdir(data_dir) if f.endswith(".csv")]
        logger.info(f"Found {len(csv_files)} CSV files: {csv_files}")

        all_scenarios = []

        for csv_file in csv_files:
            csv_path = os.path.join(data_dir, csv_file)
            logger.info(f"Processing {csv_file}")

            try:
                df = pd.read_csv(csv_path)
                scenarios = BenchmarkDataProcessor._process_csv_file(df, csv_file)
                all_scenarios.extend(scenarios)
                logger.info(f"  Added {len(scenarios)} scenarios from {csv_file}")
            except Exception as e:
                logger.error(f"  Error processing {csv_file}: {e}")
                continue

        # Apply limit if specified
        if max_scenarios is not None:
            all_scenarios = all_scenarios[:max_scenarios]
            logger.info(f"Limited to {len(all_scenarios)} scenarios")

        logger.info(f"Total scenarios loaded: {len(all_scenarios)}")
        return all_scenarios

    @staticmethod
    def _process_csv_file(
        df: pd.DataFrame, csv_filename: str
    ) -> List[BenchmarkScenario]:
        """Process a single CSV DataFrame into conversation scenarios."""
        scenarios = []

        for idx, row in df.iterrows():
            # Skip rows with missing essential data
            if (
                pd.isna(row.get("system_prompt"))
                or pd.isna(row.get("user_prompt"))
                or pd.isna(row.get("proposition"))
            ):
                continue

            # Extract and clean data
            system_prompt = " ".join(str(row["system_prompt"]).strip().split())
            user_prompt = " ".join(str(row["user_prompt"]).strip().split())
            proposition = str(row["proposition"]).strip()
            ground_truth = (
                str(row["ground_truth"]).strip()
                if pd.notna(row.get("ground_truth"))
                else ""
            )
            task_type = (
                str(row["type"]).strip() if pd.notna(row.get("type")) else "unknown"
            )

            # Create meaningful responses from proposition
            scheming_response, truthful_response = (
                BenchmarkDataProcessor._create_responses_from_proposition(
                    proposition, ground_truth, user_prompt
                )
            )

            if scheming_response is None or truthful_response is None:
                continue

            scenario = BenchmarkScenario(
                task_id=str(row.get("task_id", f"{csv_filename}_{idx}")),
                system=system_prompt,
                user=user_prompt,
                scheming_response=scheming_response,
                truthful_response=truthful_response,
                type=task_type,
                ground_truth=ground_truth,
                csv_source=csv_filename,
            )

            scenarios.append(scenario)

        return scenarios

    @staticmethod
    def _create_responses_from_proposition(
        proposition: str, ground_truth: str, user_prompt: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """Create scheming and truthful responses from proposition."""
        # Extract pivotal choices
        pivotal_match = re.search(r"<pivotal>(.*?)</pivotal>", proposition)
        if not pivotal_match:
            return None, None

        pivotal_content = pivotal_match.group(1)

        # Handle numerical format: <pivotal><value></pivotal>
        if pivotal_content == "<value>":
            return BenchmarkDataProcessor._handle_numerical_proposition(
                proposition, ground_truth, user_prompt
            )

        # Handle binary format: <pivotal>choice1/choice2</pivotal>
        choices = pivotal_content.split("/")
        if len(choices) != 2:
            return None, None

        choice1, choice2 = choices[0].strip(), choices[1].strip()

        # Determine which choice aligns with ground truth
        if choice1.lower() in ground_truth.lower():
            truthful_choice, scheming_choice = choice1, choice2
        elif choice2.lower() in ground_truth.lower():
            truthful_choice, scheming_choice = choice2, choice1
        else:
            # Fallback heuristics
            if "not" in ground_truth.lower():
                if "not" in choice2.lower() or "no" in choice2.lower():
                    truthful_choice, scheming_choice = choice2, choice1
                else:
                    truthful_choice, scheming_choice = choice1, choice2
            else:
                if "not" not in choice1.lower() and "no" not in choice1.lower():
                    truthful_choice, scheming_choice = choice1, choice2
                else:
                    truthful_choice, scheming_choice = choice2, choice1

        # Create contextual responses
        if any(
            phrase in user_prompt.lower()
            for phrase in ["yes or no", "answer yes or no"]
        ):
            # Simple yes/no responses based on positive/negative sentiment
            positive_words = [
                "is",
                "are",
                "was",
                "were",
                "can",
                "has",
                "have",
                "safe",
                "real",
                "true",
                "did",
                "do",
            ]
            negative_words = [
                "not",
                "no",
                "cannot",
                "dangerous",
                "fake",
                "false",
                "did not",
                "do not",
            ]

            if any(pos in scheming_choice.lower() for pos in positive_words):
                scheming_response = "Yes"
            elif any(neg in scheming_choice.lower() for neg in negative_words):
                scheming_response = "No"
            else:
                scheming_response = scheming_choice.capitalize()

            if any(pos in truthful_choice.lower() for pos in positive_words):
                truthful_response = "Yes"
            elif any(neg in truthful_choice.lower() for neg in negative_words):
                truthful_response = "No"
            else:
                truthful_response = truthful_choice.capitalize()
        else:
            # Use the choices directly or format into proposition
            base_proposition = re.sub(r"<pivotal>.*?</pivotal>", "{}", proposition)
            if len(base_proposition) > 200 or "?" in base_proposition:
                scheming_response = scheming_choice.capitalize()
                truthful_response = truthful_choice.capitalize()
            else:
                scheming_response = base_proposition.format(scheming_choice)
                truthful_response = base_proposition.format(truthful_choice)

        return scheming_response, truthful_response

    @staticmethod
    def _handle_numerical_proposition(
        proposition: str, ground_truth: str, user_prompt: str  # noqa: ARG004
    ) -> Tuple[Optional[str], Optional[str]]:
        """Handle numerical propositions with <pivotal><value></pivotal> format."""
        # Extract the numerical value from ground_truth
        # First try percentage patterns
        percent_match = re.search(r"(\d+(?:\.\d+)?)%", ground_truth)
        if percent_match:
            truthful_number = percent_match.group(1)
        else:
            # Try other number patterns with units
            number_patterns = [
                r"(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:million|billion|trillion)",
                r"(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:people|dollars|percent)",
                r"(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:years?|points?)",
                r"(\d+(?:,\d{3})*(?:\.\d+)?)",  # Fallback: any number
            ]

            truthful_number = None
            for pattern in number_patterns:
                match = re.search(pattern, ground_truth)
                if match:
                    truthful_number = match.group(1).replace(",", "")
                    break

            if truthful_number is None:
                return None, None

        try:
            truthful_value = float(truthful_number)
        except ValueError:
            return None, None

        # Create a contrasting scheming value
        if "%" in ground_truth or "percent" in ground_truth.lower():
            # For percentages, create a contrasting value
            if truthful_value < 50:
                scheming_value = min(95, truthful_value * 2.5)  # Make it higher
            else:
                scheming_value = max(5, truthful_value * 0.4)  # Make it lower
            scheming_number = f"{scheming_value:.1f}".rstrip("0").rstrip(".")
            truthful_number = f"{truthful_value:.1f}".rstrip("0").rstrip(".")
        elif "million" in ground_truth.lower() or "billion" in ground_truth.lower():
            # For large numbers, create significant contrast
            if truthful_value < 100:
                scheming_value = truthful_value * 1.8
            else:
                scheming_value = truthful_value * 0.6
            scheming_number = f"{scheming_value:.1f}".rstrip("0").rstrip(".")
            truthful_number = f"{truthful_value:.1f}".rstrip("0").rstrip(".")
        else:
            # For other numbers, create reasonable contrast
            if truthful_value < 1000:
                scheming_value = truthful_value * 1.5
            else:
                scheming_value = truthful_value * 0.7
            scheming_number = f"{int(scheming_value):,}"
            truthful_number = f"{int(truthful_value):,}"

        # Create responses by substituting the <value> placeholder
        base_proposition = proposition.replace("<pivotal><value></pivotal>", "{}")

        scheming_response = base_proposition.format(scheming_number)
        truthful_response = base_proposition.format(truthful_number)

        return scheming_response, truthful_response


class BenchmarkActivationExtractor:
    """Extracts activations from benchmark scenarios."""

    @staticmethod
    def extract_activations(
        scenarios: List[BenchmarkScenario],
        model,
        tokenizer,
        target_layer: int,
        response_type: str,
        device: str,
    ) -> torch.Tensor:
        """Extract activations from assistant response tokens."""
        logger.info(
            f"Extracting {response_type} activations from {len(scenarios)} scenarios"
        )

        activations = []
        hook_name = f"model.layers.{target_layer}.mlp"

        def hook_fn(module, input, output):  # noqa: ARG001
            if isinstance(output, tuple):
                activations.append(output[0][:, -1, :].detach())
            else:
                activations.append(output[:, -1, :].detach())

        # Register hook
        target_module = model
        for part in hook_name.split("."):
            target_module = getattr(target_module, part)
        hook_handle = target_module.register_forward_hook(hook_fn)

        try:
            with torch.no_grad():
                for i, scenario in enumerate(scenarios):
                    if i % 50 == 0:
                        logger.info(f"  Processing scenario {i+1}/{len(scenarios)}")

                    # Format conversation with chat template
                    conversation = [
                        {"role": "system", "content": scenario.system},
                        {"role": "user", "content": scenario.user},
                    ]

                    prompt = tokenizer.apply_chat_template(
                        conversation, tokenize=False, add_generation_prompt=True
                    )

                    # Tokenize context
                    context_tokens = tokenizer(
                        prompt, return_tensors="pt", truncation=True, max_length=512
                    )
                    context_ids = context_tokens["input_ids"].to(device)

                    # Get target response
                    target_response = getattr(scenario, f"{response_type}_response")
                    response_tokens = tokenizer(
                        target_response, return_tensors="pt", add_special_tokens=False
                    )
                    response_ids = response_tokens["input_ids"].to(device)

                    # Combine and forward pass
                    full_ids = torch.cat([context_ids, response_ids], dim=1)
                    _ = model(full_ids)

        finally:
            hook_handle.remove()

        if not activations:
            raise ValueError(f"No activations extracted for {response_type}")

        result = torch.cat(activations, dim=0)
        logger.info(f"Extracted {result.shape[0]} {response_type} activations")
        return result


class SteeringPipeline:
    """Main pipeline for creating functional activations from benchmark data."""

    def __init__(self, config: SteeringPipelineConfig):
        """Initialize the pipeline with configuration."""
        self.config = config
        self.model = None
        self.tokenizer = None
        self.sae = None
        self.steering = None
        self.scenarios = None
        self.mi_result = None
        self.functional_activations = None

        # Setup device
        if self.config.device == "auto":
            self.config.device = "cuda" if torch.cuda.is_available() else "cpu"

        # Auto-detect number of processes if not specified
        if self.config.num_processes is None:
            self.config.num_processes = 1 if self.config.device == "cuda" else 4

        logger.info(f"Initialized steering pipeline with device: {self.config.device}")

    def load_model_and_sae(self):
        """Load the model, tokenizer, and SAE."""
        logger.info("📥 Loading model and tokenizer...")
        model_config = ModelConfig(
            model_name=self.config.model_name, load_for_generation=True
        )
        model_loader = UniversalModelLoader(model_config)
        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()

        logger.info("📥 Loading SAE...")
        self.sae = SAE.load(
            self.config.sae_path,
            device=self.config.device,
            folder_name=self.config.sae_folder_name,
        )

        logger.info("✅ Model and SAE loaded successfully")

    def load_data(self):
        """Load and process benchmark data."""
        logger.info("📊 Loading training data...")
        self.scenarios = BenchmarkDataProcessor.load_and_process_data(
            self.config.train_data_dir, self.config.max_train_scenarios
        )
        logger.info(f"✅ Loaded {len(self.scenarios)} scenarios")

    def extract_activations(self):
        """Extract scheming and truthful activations."""
        if self.model is None or self.scenarios is None:
            raise ValueError("Model and scenarios must be loaded first")

        logger.info("🧠 Extracting training activations...")

        self.scheming_activations = BenchmarkActivationExtractor.extract_activations(
            self.scenarios,
            self.model,
            self.tokenizer,
            self.config.target_layer,
            "scheming",
            self.config.device,
        )

        self.truthful_activations = BenchmarkActivationExtractor.extract_activations(
            self.scenarios,
            self.model,
            self.tokenizer,
            self.config.target_layer,
            "truthful",
            self.config.device,
        )

        logger.info("✅ Activations extracted successfully")

    def create_steering(self):
        """Create steering intervention and functional activations."""
        if self.sae is None:
            raise ValueError("SAE must be loaded first")

        logger.info("🎯 Creating steering intervention...")

        self.steering = KnowledgeSelectionSteering(
            self.sae, device=self.config.device, num_processes=self.config.num_processes
        )

        logger.info(
            f"Using {self.config.num_processes} process(es) for mutual information calculation"
        )

        self.mi_result = self.steering.calculate_mutual_information(
            scheming_hiddens=self.scheming_activations,
            truthful_hiddens=self.truthful_activations,
            top_k_proportion=self.config.top_k_proportion,
            minmax_normalization=True,
            equal_label_examples=True,
        )

        self.functional_activations = self.steering.create_functional_activations(
            self.mi_result
        )

        logger.info("✅ Steering intervention created successfully")

    def save_results(self) -> Tuple[str, str]:
        """Save functional activations and configuration."""
        if self.steering is None or self.functional_activations is None:
            raise ValueError("Steering must be created first")

        # Create results directory
        os.makedirs(self.config.results_dir, exist_ok=True)

        logger.info("💾 Saving functional activations...")

        # Save functional activations
        activations_save_path = os.path.join(
            self.config.results_dir,
            f"functional_activations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pt",
        )

        self.steering.save_results(
            save_path=activations_save_path,
            mi_result=self.mi_result,
            functional_activations=self.functional_activations,
        )

        # Save configuration
        config_save_path = os.path.join(
            self.config.results_dir,
            f"steering_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        )

        config_dict = {
            "model_name": self.config.model_name,
            "sae_path": self.config.sae_path,
            "sae_folder_name": self.config.sae_folder_name,
            "target_layer": self.config.target_layer,
            "steering_alpha": self.config.steering_alpha,
            "top_k_proportion": self.config.top_k_proportion,
            "train_scenarios": len(self.scenarios) if self.scenarios else 0,
            "max_new_tokens": self.config.max_new_tokens,
            "activations_file": activations_save_path,
            "timestamp": datetime.now().isoformat(),
        }

        with open(config_save_path, "w") as f:
            json.dump(config_dict, f, indent=2)

        logger.info(f"✅ Functional activations saved to {activations_save_path}")
        logger.info(f"✅ Configuration saved to {config_save_path}")

        return activations_save_path, config_save_path

    def run_full_pipeline(self) -> Tuple[str, str]:
        """Run the complete pipeline from start to finish."""
        logger.info("🚀 Starting MASK Benchmark Truthfulness Steering Pipeline")

        try:
            self.load_model_and_sae()
            self.load_data()
            self.extract_activations()
            self.create_steering()
            activations_path, config_path = self.save_results()

            logger.info("✅ Pipeline completed successfully!")
            logger.info("Functional activations are ready for use in other scripts.")

            return activations_path, config_path

        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
            raise

    @classmethod
    def from_config_file(cls, config_path: str) -> "SteeringPipeline":
        """Create pipeline from a saved configuration file."""
        with open(config_path, "r") as f:
            config_dict = json.load(f)

        config = SteeringPipelineConfig(
            model_name=config_dict["model_name"],
            sae_path=config_dict["sae_path"],
            target_layer=config_dict.get("target_layer", 16),
            steering_alpha=config_dict.get("steering_alpha", 1.5),
            top_k_proportion=config_dict.get("top_k_proportion", 0.15),
            max_new_tokens=config_dict.get("max_new_tokens", 1024),
            sae_folder_name=config_dict.get("sae_folder_name"),
        )

        return cls(config)

    @classmethod
    def load_from_saved_activations(
        cls, activations_path: str, config_path: str
    ) -> Tuple["SteeringPipeline", KnowledgeSelectionSteering, FunctionalActivations]:
        """Load pipeline from saved activations and config."""
        # Load config
        with open(config_path, "r") as f:
            config_dict = json.load(f)

        # Create pipeline instance
        config = SteeringPipelineConfig(
            model_name=config_dict["model_name"],
            sae_path=config_dict["sae_path"],
            target_layer=config_dict.get("target_layer", 16),
            steering_alpha=config_dict.get("steering_alpha", 1.5),
            top_k_proportion=config_dict.get("top_k_proportion", 0.15),
            max_new_tokens=config_dict.get("max_new_tokens", 1024),
            sae_folder_name=config_dict.get("sae_folder_name"),
        )

        pipeline = cls(config)

        # Load SAE for the steering
        sae = SAE.load(
            config.sae_path, device=config.device, folder_name=config.sae_folder_name
        )

        # Load functional activations
        steering, mi_result, functional_activations = (
            KnowledgeSelectionSteering.load_results(
                load_path=activations_path,
                sae=sae,
                device=config.device,
                num_processes=config.num_processes,
            )
        )

        # Set the loaded components
        pipeline.sae = sae
        pipeline.steering = steering
        pipeline.mi_result = mi_result
        pipeline.functional_activations = functional_activations

        logger.info("✅ Pipeline loaded from saved activations")

        return pipeline, steering, functional_activations
